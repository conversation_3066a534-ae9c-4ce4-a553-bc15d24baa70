import { Badge } from '@/components/ui/badge';
import type { ClientTag } from '../types';

interface TagDisplayProps {
  tags: ClientTag[];
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const TagDisplay = ({ 
  tags, 
  maxVisible = 3, 
  size = 'sm',
  className = "" 
}: TagDisplayProps) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  const visibleTags = tags.slice(0, maxVisible);
  const hiddenCount = tags.length - maxVisible;

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5'
  };

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {visibleTags.map(tag => (
        <Badge
          key={tag.id}
          variant="outline"
          className={`${sizeClasses[size]} border`}
          style={{ 
            backgroundColor: `${tag.color}15`, 
            borderColor: tag.color,
            color: tag.color
          }}
        >
          {tag.name}
        </Badge>
      ))}
      
      {hiddenCount > 0 && (
        <Badge
          variant="outline"
          className={`${sizeClasses[size]} text-gray-500 border-gray-300`}
        >
          +{hiddenCount}
        </Badge>
      )}
    </div>
  );
};

export default TagDisplay;
