import { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, Plus } from 'lucide-react';
import type { ClientTag } from '../types';

interface TagInputProps {
  selectedTags: ClientTag[];
  availableTags: ClientTag[];
  onTagsChange: (tags: ClientTag[]) => void;
  onCreateTag: (name: string) => Promise<ClientTag>;
  placeholder?: string;
  className?: string;
}

const TagInput = ({
  selectedTags,
  availableTags,
  onTagsChange,
  onCreateTag,
  placeholder = "Add tags...",
  className = ""
}: TagInputProps) => {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter available tags to show only unselected ones that match input
  const filteredSuggestions = availableTags.filter(tag => 
    !selectedTags.some(selected => selected.id === tag.id) &&
    tag.name.toLowerCase().includes(inputValue.toLowerCase())
  );

  // Check if input matches an existing tag exactly
  const exactMatch = availableTags.find(tag => 
    tag.name.toLowerCase() === inputValue.toLowerCase()
  );

  const handleInputChange = (value: string) => {
    setInputValue(value);
    setShowSuggestions(value.length > 0);
  };

  const handleTagSelect = (tag: ClientTag) => {
    if (!selectedTags.some(selected => selected.id === tag.id)) {
      onTagsChange([...selectedTags, tag]);
    }
    setInputValue('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleTagRemove = (tagToRemove: ClientTag) => {
    onTagsChange(selectedTags.filter(tag => tag.id !== tagToRemove.id));
  };

  const handleCreateTag = async () => {
    if (!inputValue.trim() || exactMatch || isCreating) return;

    try {
      setIsCreating(true);
      const newTag = await onCreateTag(inputValue.trim());
      handleTagSelect(newTag);
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (filteredSuggestions.length > 0) {
        handleTagSelect(filteredSuggestions[0]);
      } else if (inputValue.trim() && !exactMatch) {
        handleCreateTag();
      }
    } else if (e.key === 'Backspace' && !inputValue && selectedTags.length > 0) {
      handleTagRemove(selectedTags[selectedTags.length - 1]);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setInputValue('');
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Selected Tags */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {selectedTags.map(tag => (
            <Badge
              key={tag.id}
              variant="secondary"
              className="flex items-center gap-1 text-xs"
              style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
            >
              <span style={{ color: tag.color }}>{tag.name}</span>
              <button
                type="button"
                onClick={() => handleTagRemove(tag)}
                className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* Input */}
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSuggestions(inputValue.length > 0)}
          placeholder={placeholder}
          className="w-full"
        />

        {/* Suggestions Dropdown */}
        {showSuggestions && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
            {filteredSuggestions.length > 0 && (
              <div className="py-1">
                {filteredSuggestions.map(tag => (
                  <button
                    key={tag.id}
                    type="button"
                    onClick={() => handleTagSelect(tag)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center gap-2"
                  >
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span className="text-sm">{tag.name}</span>
                  </button>
                ))}
              </div>
            )}

            {/* Create new tag option */}
            {inputValue.trim() && !exactMatch && (
              <div className="border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleCreateTag}
                  disabled={isCreating}
                  className="w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center gap-2 text-sm text-gray-600"
                >
                  <Plus className="h-3 w-3" />
                  <span>
                    {isCreating ? 'Creating...' : `Create "${inputValue.trim()}"`}
                  </span>
                </button>
              </div>
            )}

            {filteredSuggestions.length === 0 && (!inputValue.trim() || exactMatch) && (
              <div className="px-3 py-2 text-sm text-gray-500">
                {!inputValue.trim() ? 'Start typing to search tags...' : 'Tag already exists'}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TagInput;
