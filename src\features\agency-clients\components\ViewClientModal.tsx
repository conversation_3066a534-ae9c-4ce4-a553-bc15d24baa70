import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Building2,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Briefcase,
  Loader2,
  Edit
} from 'lucide-react';
import { useToast } from '@/core';
import { getAgencyClient } from '../api/clientService';
import { TagDisplay } from './index';
import type { AgencyClientWithContacts } from '../types';

interface ViewClientModalProps {
  open: boolean;
  onClose: () => void;
  onEdit?: (client: AgencyClientWithContacts) => void;
  clientId: string | null;
}

const ViewClientModal = ({ open, onClose, onEdit, clientId }: ViewClientModalProps) => {
  const { toast } = useToast();
  const [client, setClient] = useState<AgencyClientWithContacts | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadClient = async () => {
      if (!clientId || !open) {
        setClient(null);
        return;
      }

      setLoading(true);
      try {
        const clientData = await getAgencyClient(clientId);
        setClient(clientData);
      } catch (error) {
        console.error('Error loading client:', error);
        toast({
          title: 'Error',
          description: 'Failed to load client information.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadClient();
  }, [clientId, open]);

  const handleEdit = () => {
    if (client && onEdit) {
      onEdit(client);
      onClose();
    }
  };

  const getClientIcon = (type: string) => {
    return type === 'company' ?
      <Building2 className="h-5 w-5 text-blue-500" /> :
      <User className="h-5 w-5 text-green-500" />;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {client && getClientIcon(client.type)}
            Client Details
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading client information...</span>
          </div>
        ) : client ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                {getClientIcon(client.type)}
                <h3 className="text-lg font-semibold text-gray-900">{client.name}</h3>
                <Badge variant={client.type === 'company' ? 'default' : 'secondary'}>
                  {client.type === 'company' ? 'Company' : 'Individual'}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {client.email && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{client.email}</span>
                  </div>
                )}
                {client.phone && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{client.phone}</span>
                  </div>
                )}
              </div>

              {client.address && (
                <div className="flex items-start gap-2 text-gray-600">
                  <MapPin className="h-4 w-4 mt-0.5" />
                  <span>{client.address}</span>
                </div>
              )}

              {client.vat_number && (
                <div className="flex items-center gap-2 text-gray-600">
                  <FileText className="h-4 w-4" />
                  <span>VAT: {client.vat_number}</span>
                </div>
              )}

              {/* Tags */}
              {client.tags && client.tags.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Tags</h4>
                  <TagDisplay tags={client.tags} maxVisible={5} size="md" />
                </div>
              )}

              {client.notes && (
                <div className="space-y-2">
                  <Separator />
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Notes</h4>
                    <p className="text-gray-600 text-sm whitespace-pre-wrap">{client.notes}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Contacts Section (only for companies) */}
            {client.type === 'company' && (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  <h4 className="text-base font-medium">Contacts</h4>
                  {client.contacts && client.contacts.length > 0 && (
                    <Badge variant="outline">
                      {client.contacts.length} contact{client.contacts.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>

                {client.contacts && client.contacts.length > 0 ? (
                  <div className="space-y-3">
                    {client.contacts.map((contact, index) => (
                      <Card key={contact.id}>
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2 mb-3">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{contact.name}</span>
                            {contact.is_primary && (
                              <Badge variant="default" className="text-xs">
                                Primary
                              </Badge>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm text-gray-600">
                            {contact.email && (
                              <div className="flex items-center gap-2">
                                <Mail className="h-3 w-3" />
                                <span>{contact.email}</span>
                              </div>
                            )}
                            {contact.phone && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-3 w-3" />
                                <span>{contact.phone}</span>
                              </div>
                            )}
                            {contact.position && (
                              <div className="flex items-center gap-2">
                                <Briefcase className="h-3 w-3" />
                                <span>{contact.position}</span>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <User className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No contacts added yet</p>
                  </div>
                )}
              </div>
            )}

            {/* Metadata */}
            <div className="pt-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {new Date(client.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
                <div>
                  <span className="font-medium">Last Updated:</span>{' '}
                  {new Date(client.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p>Client not found</p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {client && onEdit && (
            <Button onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Client
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewClientModal;
