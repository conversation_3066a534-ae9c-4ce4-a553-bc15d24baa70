import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Plus, Search, Building2, User, Mail, Phone, MoreVertical, Eye, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast, useIsMobile } from '@/core';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/core/api/supabase';
import { deleteAgencyClient, getAgencyTags } from '@/features/agency-clients/api/clientService';
import { ClientModal, TagDisplay } from '@/features/agency-clients/components';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import type { AgencyClientWithContacts, ClientTag } from '@/features/agency-clients/types';

const AgencyClients = () => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const navigate = useNavigate();

  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedTagFilter, setSelectedTagFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showClientModal, setShowClientModal] = useState(false);
  const [editingClient, setEditingClient] = useState<AgencyClientWithContacts | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<AgencyClientWithContacts | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const rowsPerPage = 10;

  // Fetch available tags
  const { data: availableTags = [] } = useQuery({
    queryKey: ['agencyTags'],
    queryFn: async () => {
      try {
        if (!profile) return [];

        const { data: agencyEntities, error: entityError } = await supabase
          .from('entity_users')
          .select('entity_id')
          .eq('user_id', profile.id);

        if (entityError || !agencyEntities || agencyEntities.length === 0) {
          return [];
        }

        const agencyEntityId = agencyEntities[0].entity_id;
        return await getAgencyTags(agencyEntityId);
      } catch (error) {
        console.error('Error fetching tags:', error);
        return [];
      }
    },
    enabled: !!profile
  });

  // Fetch clients using React Query (similar to Paperwork page)
  const {
    data: clients = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['agencyClients'],
    queryFn: async () => {
      try {
        if (!profile) throw new Error('Not authenticated');

        // Get user's agency entities from entity_users table (same pattern as Paperwork)
        const { data: agencyEntities, error: entityError } = await supabase
          .from('entity_users')
          .select('entity_id')
          .eq('user_id', profile.id);

        if (entityError) {
          console.error('Error fetching agency entities:', entityError);
          throw entityError;
        }

        if (!agencyEntities || agencyEntities.length === 0) {
          return [];
        }

        // Get the first agency entity ID
        const agencyEntityId = agencyEntities[0].entity_id;

        // Fetch clients for this agency with tags
        const { data: clientsData, error: clientsError } = await (supabase as any)
          .from('agency_clients_with_contacts')
          .select('*')
          .eq('agency_entity_id', agencyEntityId)
          .order('name');

        if (clientsError) {
          console.error('Error fetching clients:', clientsError);
          throw clientsError;
        }

        // Deduplicate contacts and tags for each client
        const deduplicatedClients = (clientsData || []).map((client: any) => {
          const uniqueContacts = client.contacts ?
            client.contacts.filter((contact: any, index: number, self: any[]) =>
              index === self.findIndex((c: any) => c.id === contact.id)
            ) : [];

          const uniqueTags = client.tags ?
            client.tags.filter((tag: any, index: number, self: any[]) =>
              index === self.findIndex((t: any) => t.id === tag.id)
            ) : [];

          return {
            ...client,
            contacts: uniqueContacts,
            tags: uniqueTags
          };
        });

        return deduplicatedClients;
      } catch (err) {
        console.error('Error fetching clients:', err);
        toast({
          title: 'Error',
          description: 'Failed to load clients. Please try again.',
          variant: 'destructive',
        });
        return [];
      }
    },
    enabled: !!profile
  });

  // Filter clients based on search, type, and tags
  const filteredClients = clients.filter(client => {
    const matchesSearch = !searchQuery ||
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (client.email && client.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (client.tags && client.tags.some(tag =>
        tag.name.toLowerCase().includes(searchQuery.toLowerCase())
      ));

    const matchesType = typeFilter === 'all' || client.type === typeFilter;

    const matchesTag = selectedTagFilter === 'all' ||
      (client.tags && client.tags.some(tag => tag.id === selectedTagFilter));

    return matchesSearch && matchesType && matchesTag;
  });

  // Pagination
  const pageCount = Math.ceil(filteredClients.length / rowsPerPage);
  const indexOfLastItem = currentPage * rowsPerPage;
  const indexOfFirstItem = indexOfLastItem - rowsPerPage;
  const currentItems = filteredClients.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddClient = () => {
    setModalMode('create');
    setEditingClient(null);
    setShowClientModal(true);
  };

  const handleViewClient = (client: AgencyClientWithContacts) => {
    navigate(`/agency/clients/${client.id}`);
  };

  const handleEditClient = (client: AgencyClientWithContacts) => {
    setModalMode('edit');
    setEditingClient(client);
    setShowClientModal(true);
  };

  const handleDeleteClient = (client: AgencyClientWithContacts) => {
    setClientToDelete(client);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!clientToDelete) return;

    try {
      setIsDeleting(true);
      await deleteAgencyClient(clientToDelete.id);

      toast({
        title: 'Success',
        description: 'Client deleted successfully.',
      });

      refetch(); // Refresh the clients list
    } catch (error) {
      console.error('Error deleting client:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete client. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setClientToDelete(null);
    }
  };

  const handleModalSuccess = () => {
    refetch(); // Refresh the clients list
  };

  const handleModalClose = () => {
    setShowClientModal(false);
    setEditingClient(null);
  };

  const getClientIcon = (type: string) => {
    return type === 'company' ?
      <Building2 className="h-4 w-4 mr-2 text-blue-500" /> :
      <User className="h-4 w-4 mr-2 text-green-500" />;
  };

  return (
    <DashboardLayout userType="agency">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-4">
          {/* Top row with title */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold tracking-tight">Clients</h1>
            {/* Desktop button - hidden on mobile */}
            <div className="hidden sm:flex">
              <Button
                onClick={handleAddClient}
                className="bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Client
              </Button>
            </div>
          </div>

          {/* Mobile button - shown only on mobile */}
          <div className="flex sm:hidden">
            <Button
              onClick={handleAddClient}
              className="flex-1 bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Client
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Client Management</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="mb-6">
              {isMobile ? (
                <div className="space-y-3">
                  {/* Search bar - full width on mobile */}
                  <div className="relative w-full">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search clients, emails, tags..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="pl-8 w-full"
                    />
                  </div>

                  {/* Filter controls */}
                  <div className="grid grid-cols-1 gap-2">
                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Client Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All types</SelectItem>
                        <SelectItem value="company">Companies</SelectItem>
                        <SelectItem value="person">Individuals</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={selectedTagFilter} onValueChange={setSelectedTagFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Filter by Tag" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All tags</SelectItem>
                        {availableTags.map(tag => (
                          <SelectItem key={tag.id} value={tag.id}>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: tag.color }}
                              />
                              {tag.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-3 items-center">
                  <div className="relative flex-1 min-w-[200px]">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search clients, emails, tags..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="pl-8"
                    />
                  </div>

                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Client Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value="company">Companies</SelectItem>
                      <SelectItem value="person">Individuals</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={selectedTagFilter} onValueChange={setSelectedTagFilter}>
                    <SelectTrigger className="w-[160px]">
                      <SelectValue placeholder="Filter by Tag" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All tags</SelectItem>
                      {availableTags.map(tag => (
                        <SelectItem key={tag.id} value={tag.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: tag.color }}
                            />
                            {tag.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* Clients Table/List */}
            <div>
              {isMobile ? (
                <div className="flex flex-col space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center items-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                      <span className="ml-2 text-sm text-gray-600">Loading...</span>
                    </div>
                  ) : currentItems.length > 0 ? (
                    currentItems.map(client => (
                      <div
                        key={client.id}
                        className="border rounded-md p-2 cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => handleViewClient(client)}
                      >
                        {/* Header: Name and Type Badge */}
                        <div className="flex justify-between items-start mb-1">
                          <h4 className="font-medium text-sm truncate pr-2">{client.name}</h4>
                          <Badge variant={client.type === 'company' ? 'default' : 'secondary'} className="text-xs px-1.5 py-0 h-5 flex-shrink-0">
                            {client.type === 'company' ? 'Co' : 'Ind'}
                          </Badge>
                        </div>

                        {/* Contact info - single line */}
                        <div className="flex items-center text-xs text-gray-600 mb-1">
                          {client.email && (
                            <div className="flex items-center mr-3 truncate">
                              <Mail className="h-3 w-3 mr-1 flex-shrink-0" />
                              <span className="truncate">{client.email}</span>
                            </div>
                          )}
                          {client.phone && (
                            <div className="flex items-center flex-shrink-0">
                              <Phone className="h-3 w-3 mr-1" />
                              <span>{client.phone}</span>
                            </div>
                          )}
                        </div>

                        {/* Tags and Actions */}
                        <div className="flex justify-between items-center">
                          <div className="flex-1 min-w-0">
                            {client.tags && client.tags.length > 0 ? (
                              <TagDisplay tags={client.tags} maxVisible={2} />
                            ) : (
                              <div className="h-5"></div>
                            )}
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 flex-shrink-0">
                                <span className="sr-only">Open menu</span>
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                handleViewClient(client);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                <span>View Details</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                handleEditClient(client);
                              }}>
                                <Edit className="mr-2 h-4 w-4" />
                                <span>Edit Client</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600" onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteClient(client);
                              }}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 bg-gray-50 rounded-lg">
                      <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-gray-500 text-sm">
                        {error ? 'Error loading clients' : 'No clients found'}
                      </p>
                      {!searchQuery && typeFilter === 'all' && selectedTagFilter === 'all' && (
                        <Button
                          onClick={handleAddClient}
                          className="mt-3 text-xs py-1 h-8 bg-stagecloud-black hover:bg-stagecloud-purple/90"
                        >
                          <Plus className="mr-1.5 h-3 w-3" />
                          Add Client
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Client</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Tags</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Phone</TableHead>
                          <TableHead>Address</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoading ? (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8">
                              Loading clients...
                            </TableCell>
                          </TableRow>
                        ) : currentItems.length > 0 ? (
                          currentItems.map(client => (
                            <TableRow
                              key={client.id}
                              className="hover:bg-muted/50 transition-colors cursor-pointer"
                              onClick={() => handleViewClient(client)}
                            >
                              <TableCell className="font-medium">
                                <div className="flex items-center">
                                  {getClientIcon(client.type)}
                                  <span>{client.name}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={client.type === 'company' ? 'default' : 'secondary'}>
                                  {client.type === 'company' ? 'Company' : 'Individual'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {client.tags && client.tags.length > 0 ? (
                                  <TagDisplay tags={client.tags} maxVisible={2} />
                                ) : (
                                  <span className="text-gray-400">-</span>
                                )}
                              </TableCell>
                              <TableCell>{client.email || '-'}</TableCell>
                              <TableCell>{client.phone || '-'}</TableCell>
                              <TableCell className="max-w-[200px] truncate">{client.address || '-'}</TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <span className="sr-only">Open menu</span>
                                      <MoreVertical className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      handleViewClient(client);
                                    }}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View Details</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditClient(client);
                                    }}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      <span>Edit Client</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600" onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteClient(client);
                                    }}>
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                              {error ? 'Error loading clients' : 'No clients found matching your filters'}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>


                </>
              )}
            </div>

            {/* Pagination - matches bookings page structure */}
            <div className="mt-6 flex flex-col sm:flex-row gap-4 items-center justify-between">
              {isMobile ? (
                <div className="w-full flex justify-between items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-2"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <span className="text-sm">
                    Page {currentPage} of {pageCount || 1}
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === pageCount || pageCount === 0}
                    className="px-2"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Rows per page:</span>
                    <Select value={String(rowsPerPage)} onValueChange={value => {
                      const newRowsPerPage = parseInt(value);
                      // Note: rowsPerPage is const in this component, so this would need to be made state
                      // For now, keeping the structure consistent but this would need adjustment
                      setCurrentPage(1);
                    }}>
                      <SelectTrigger className="h-8 w-[70px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="15">15</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {filteredClients.length > rowsPerPage && (
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                        </PaginationItem>

                        {Array.from({ length: Math.min(pageCount, 5) }, (_, i) => {
                          let pageNum: number;
                          if (pageCount <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= pageCount - 2) {
                            pageNum = pageCount - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }
                          return (
                            <PaginationItem key={i}>
                              <PaginationLink onClick={() => handlePageChange(pageNum)} isActive={pageNum === currentPage}>
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}

                        <PaginationItem>
                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className={currentPage === pageCount ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client Modal */}
      <ClientModal
        open={showClientModal}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
        client={editingClient}
        mode={modalMode}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteConfirm}
        title="Delete Client"
        description={`Are you sure you want to delete "${clientToDelete?.name}"? This will also delete all associated contacts and cannot be undone.`}
        itemType="client"
        isDeleting={isDeleting}
      />
    </DashboardLayout>
  );
};

export default AgencyClients;
